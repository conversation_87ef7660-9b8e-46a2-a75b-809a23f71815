SKORE

Usage: skore

The skore command displays an enhanced version of your character information
with improved visual formatting, color coding, and organized information panels.

FEATURES:

Visual Enhancements:
- Progress bars for Hit Points, Movement, and PSP
- Health-based color coding (green=healthy, yellow=wounded, red=critical)
- Class-based color themes for different character classes
- Organized information panels with clear sections

Information Sections:
- Character Identity: Name, race, class, alignment, age, size
- Vitals & Condition: HP, Movement, PSP, physical stats, carrying capacity
- Experience & Progression: Experience points, level progress, caster levels
- Ability Scores & Saves: All six abilities with bonuses and saving throws
- Combat Statistics: BAB, attacks, AC, damage reduction, weapon information
- Magic & Psionics: Spell bonuses, psionic information (if applicable)
- Wealth & Achievements: Gold, bank, quest points, crafting jobs
- Equipment Status: Key equipment and item counts (full density mode)

Customization:
The skore display can be customized using the 'scoreconfig' command:
- Adjust display width (80, 120, or 160 characters)
- Change color themes (enhanced, classic, minimal)
- Control information density (full, compact, minimal)
- Toggle colors on/off
- Switch back to classic score format

Progress Bars:
Visual progress bars show current/maximum values:
[████████░░] 80%  - 80% of maximum
Colors change based on health:
- Green: 75%+ (healthy)
- Yellow: 50-74% (wounded)
- Orange: 25-49% (badly wounded)  
- Red: 0-24% (critical)

Class Colors:
Different character classes use themed colors:
- Blue: Arcane casters (Wizard, Sorcerer, Bard)
- Green: Divine casters (Cleric, Druid, Paladin, Ranger)
- Red: Martial classes (Warrior, Berserker, Monk)
- Magenta: Skill-based classes (Rogue)
- Cyan: Other classes

EXAMPLES:

  skore
    Shows your enhanced character information

  scoreconfig classic on
    Switches to classic score display

  scoreconfig width 120
    Uses wider display format

RELATED COMMANDS:

  score       - Classic character information display
  scoreconfig - Customize the enhanced display
  equipment   - Detailed equipment listing
  affects     - Current spell effects

The enhanced score display is part of the Phase 1 MVP implementation
providing improved visual hierarchy and better information organization
while maintaining compatibility with all MUD clients.

See also: SCORE, SCORECONFIG, EQUIPMENT, AFFECTS
