SCORECONFIG

Usage: scoreconfig [option] [value]

The scoreconfig command allows you to customize your enhanced score display.
When used without arguments, it shows your current configuration.

OPTIONS:

  width <80|120|160>
    Sets the display width for your score. Choose from:
    - 80:  Standard width (default)
    - 120: Wide display for larger terminals
    - 160: Extra wide display

  theme <enhanced|classic|minimal>
    Sets the color theme for your score display:
    - enhanced: Full color theme with class-based colors (default)
    - classic:  Traditional color scheme
    - minimal:  Reduced color usage

  density <full|compact|minimal>
    Controls how much information is displayed:
    - full:    All available information (default)
    - compact: Condensed layout with key information
    - minimal: Only essential statistics

  classic <on|off>
    Toggles between enhanced and classic score display:
    - on:  Use the original score command format
    - off: Use the enhanced skore display (default)

  colors <on|off>
    Enables or disables color in the score display:
    - on:  Show colors (default)
    - off: Plain text display

  reset
    Resets all score display settings to their defaults.

EXAMPLES:

  scoreconfig width 120
    Sets display width to 120 characters

  scoreconfig theme minimal
    Uses minimal color theme

  scoreconfig classic on
    Switches to classic score display

  scoreconfig reset
    Resets all settings to defaults

RELATED COMMANDS:

  score  - Shows character information (classic format)
  skore  - Shows enhanced character information
  toggle - Other display preferences

The enhanced score display includes:
- Visual progress bars for HP, Movement, and PSP
- Health-based color coding
- Class-based color themes
- Organized information panels
- Equipment status display
- Enhanced visual hierarchy

Your preferences are automatically saved when changed.

See also: SCORE, SKORE, TOGGLE
