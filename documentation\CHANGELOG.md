# CHANGELOG

## 2025-07-27

### Features
- **Spell Collection Display Stacking**: Implemented spell stacking in the spell collection display for all spell-preparing classes (<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Alchemist). Modified `print_collection()` in spell_prep.c to group identical spells (same spell, metamagic, and domain) and display them with a count (e.g., "ball of lightning [quickened] x7"). The preparation queue display remains unchanged to preserve individual spell ordering for future prioritization features.

### Bug Fixes
- **Fixed Array Bounds Violation in Spell Slots**: Fixed critical array bounds violation in `assign_feat_spell_slots()` function in spell_prep.c that could cause segmentation faults. Added bounds checking to ensure level_counter doesn't exceed the slot array size (84 entries), and added safety checks to prevent negative array indexing when accessing level_counter-1. This prevents crashes when character levels exceed expected bounds due to bonuses or other modifiers.
- **Fixed Use-After-Free in Spell Preparation Event**: Fixed critical use-after-free vulnerability in `event_preparation()` function in spell_prep.c where the event system could access a freed character pointer. Added validation to check if the character still exists in the global character_list before processing the event. This prevents crashes when a character is extracted/freed while having active spell preparation events.
- **Fixed Domain Array Bounds Checking**: Fixed potential array bounds violations in spell_prep.c when accessing domain_list array. Added validation to ensure domain values are within valid range (0 < domain < NUM_DOMAINS) before array access in three locations: print_prep_queue(), print_innate_magic_queue(), and print_collection(). This prevents crashes from invalid domain values.
- **Fixed Integer Underflow in star_circlet_proc()**: Fixed potential crash in `star_circlet_proc()` function in spell_prep.c where dice() could be called with num_classes=0. Added validation to check if character has any classes (num_classes > 0) before calling dice(1, num_classes). This prevents crashes when processing spell restoration for characters with no class levels.
- **Fixed Integer Overflow in Caster Level Calculation**: Fixed potential integer overflow in `known_spells_add()` function in spell_prep.c where CLASS_LEVEL + BONUS_CASTER_LEVEL could exceed array bounds. Added overflow protection that caps caster_level at 95 (safe maximum for spell known arrays) and checks for negative values. This prevents array out-of-bounds access when characters have excessive level bonuses.
- **Fixed Dangling Pointer in spell_counts Array**: Fixed potential use-after-free issue in `print_collection()` function in spell_prep.c where spell_counts array pointers were not set to NULL after freeing memory. Added NULL assignment after cleanup to prevent dangling pointer access if the array is accidentally accessed after cleanup.
- **Fixed Save/Load Input Validation**: Fixed missing validation of sscanf() return values in spell_prep.c save/load functions. Added checks in `load_spell_prep_queue()`, `load_innate_magic_queue()`, `load_spell_collection()`, and `load_known_spells()` to ensure all expected values are successfully parsed from player files. This prevents use of uninitialized values if player file data is corrupted or malformed.
- **Added Missing CREATE() NULL Checks**: Fixed missing NULL checks after CREATE() macro calls in spell_prep.c. Added defensive NULL checks in `prep_queue_add()`, `innate_magic_add()`, `collection_add()`, `known_spells_add()`, and `print_collection()` functions. This prevents crashes if memory allocation fails, logging errors and gracefully handling the failure conditions instead.
- **Fixed Buffer Overflow in print_collection()**: Fixed potential buffer overflow in `print_collection()` function in spell_prep.c where multiple strcat() calls to a fixed 256-byte buffer could exceed capacity when all metamagic flags and domain names are present. Replaced unsafe strcat() operations with safe snprintf() calls using a macro that tracks remaining buffer space. Also fixed an unsafe sprintf() used for count display. This prevents potential crashes or memory corruption when displaying spells with many metamagic modifiers.
- **Optimized star_circlet_proc() Performance**: Added loop bounds and early exit conditions to `star_circlet_proc()` function in spell_prep.c to prevent excessive iterations. Reduced LOOP_MAX from 1000 to 100, added early exit after 10 empty iterations, pre-check for classes with spells, removed unnecessary class loop, added performance monitoring in debug mode, and warning logs for hitting iteration limits. This prevents the function from consuming excessive CPU time when processing spell restoration.

### Documentation
- **Added Thread Safety Documentation**: Added comprehensive comments to spell_prep.c and spell_prep.h clarifying that LuminariMUD is single-threaded and the spell preparation system has no concurrency issues. Added comments at `event_preparation()`, event creation check, and in the header file overview to prevent future confusion about race conditions. This addresses audit issue #6 which was incorrectly identified as a potential race condition.

## 2025-07-26

### Bug Fixes
- **Fixed Post Death Bash**: Added safety checks to `perform_knockdown()` in act.offensive.c to prevent dead characters from performing bash attacks. This fixes a race condition where mobs could bash players after the mob's death but before extraction from the game. The fix checks both position (POS_DEAD) and extraction flags (DEAD() macro) for both attacker and target.
- **Fixed Clairvoyance Fall Damage**: Modified `spell_clairvoyance()` in spells.c to use `look_at_room_number()` instead of physically moving the character to the target room. This prevents the caster from taking fall damage when casting clairvoyance on targets in falling rooms (like the eternal staircase).
- **Fixed Password Echo Loop**: Fixed a race condition in interpreter.c where sending password in the same packet as username could cause echo negotiation loops. Modified all password input states (CON_PASSWORD, CON_NEWPASSWD, CON_CHPWD_GETOLD, CON_DELCNF1, CON_ACCOUNT_ADD_PWD, CON_CHPWD_GETNEW) to clear pending input queue before entering password mode, preventing premature password processing.
- **Fixed Dead NPCs Taking Damage**: Fixed issue where dead NPCs continued receiving damage from DG Script area effects. Added death check (GET_POS() <= POS_DEAD || DEAD()) to script_damage() in dg_misc.c to prevent damage to already dead characters.

### Code Cleanup
- **Commented out zone loading debug messages**: Commented out "failed percentage check" log messages in db.c for 'P', 'G', and 'E' zone commands to reduce log spam during zone resets.
