# LuminariMUD Spell Preparation System - Security and Code Quality TODO List

**Audited Files**: spell_prep.c / spell_prep.h (approximately 4500 lines)  

## TODO List - Critical Issues (PRIORITY: CRITICAL)

- [x] **Fix Array Bounds Violations in Class Slot Tables** (Issue #11)
  - Location: Lines 2832-2870
  - Added bounds checking for level_counter before accessing slot arrays
  - Added safety check for level_counter - 1 to prevent negative index
  - **COMPLETED**: 2025-07-27

- [x] **Fix Use-After-Free in Event System** (Issue #13)
  - Location: Line 2568
  - Event data allocated with strdup() but character may be freed before event fires
  - Add reference counting or validation in event handler
  - **COMPLETED**: 2025-07-27 - Added character validation in event_preparation() by checking global character_list

- [x] **Add Domain Array Bounds Checking** (Issue #2)
  - Location: Lines 3096, 3159, 3278
  - Direct array access without validation
  - Add bounds check: `if (count_entry->domain > 0 && count_entry->domain < NUM_DOMAINS)`
  - **COMPLETED**: 2025-07-27 - Added bounds checking for domain array access in all three locations

- [x] **Fix Integer Underflow in star_circlet_proc()** (Issue #15)
  - Location: Line 3689
  - dice() called with potentially zero num_classes
  - Check num_classes before dice roll
  - **COMPLETED**: 2025-07-27 - Added check for num_classes > 0 before dice() call

- [x] **Fix Integer Overflow in Caster Level Calculation** (Issue #12)
  - Location: Line 1003
  - No overflow checking when adding bonuses
  - Add overflow protection for caster_level calculation
  - **COMPLETED**: 2025-07-27 - Added overflow protection and capped caster_level at 95 (array bounds)

## TODO List - High Priority Issues

- [x] **Zero spell_counts Array After Cleanup** (Issue #3)
  - Location: Lines 3307-3318
  - After freeing spell_count_data structures, array pointers not set to NULL
  - Add `spell_counts[i] = NULL;` after cleanup
  - **COMPLETED**: 2025-07-27 - Added NULL assignment after freeing memory to prevent dangling pointers

- [x] **Add Save/Load Input Validation** (Issue #4)
  - Location: Lines 1088, 1128, 1167, 1203
  - sscanf() return value not checked
  - Validate that all expected values were read successfully
  - **COMPLETED**: 2025-07-27 - Added validation for sscanf() return values in all four load functions

- [x] **Add Missing CREATE() NULL Checks** (Issue #14)
  - Location: Lines 897, 968, and throughout
  - No NULL checks after CREATE() macro calls
  - Add defensive NULL checks after allocations
  - **COMPLETED**: 2025-07-27 - Added NULL checks for all 5 CREATE() calls in the file

## TODO List - Medium Priority Issues

- [x] **Fix Buffer Overflow in print_collection()** (Issue #1)
  - Location: Lines 3264-3282
  - Multiple strcat() calls to fixed buffer without bounds checking
  - Replace with safe string building using snprintf()
  - **COMPLETED**: 2025-07-27 - Replaced all strcat() calls with safe snprintf() operations using a SAFE_APPEND macro that tracks remaining buffer space. Also fixed the unsafe sprintf() for count display.

- [x] **Document Event System Race Conditions** (Issue #6) - **NOT APPLICABLE**
  - Location: event_preparation() function (lines ~3800-4000)
  - ~~No locking mechanism when modifying character spell queues~~
  - ~~Document single-threaded assumption or add state locking~~
  - **RESOLVED**: 2025-07-27 - Confirmed NOT an issue because:
    - LuminariMUD is single-threaded (runs in one game_loop)
    - Only ONE preparation event allowed per character (checked at line 2617)
    - No race conditions possible in single-threaded, event-driven architecture

- [x] **Add Loop Bounds to star_circlet_proc()** (Issue #7)
  - Location: Lines 3660-3771
  - Complex nested loops with potential for excessive iterations
  - Add early exit conditions and performance monitoring
  - **COMPLETED**: 2025-07-27 - Implemented several performance improvements:
    - Reduced LOOP_MAX from 1000 to 100 iterations
    - Added early exit if no classes have spells queued
    - Added MAX_EMPTY_ITERATIONS (10) counter to exit early if no spells found
    - Removed unnecessary loop through all classes (now processes only selected class)
    - Added performance monitoring in debug mode (elapsed time tracking)
    - Added warning log if LOOP_MAX is reached
    - Pre-calculate classes with spells to avoid repeated checks

## TODO List - Code Quality Improvements

- [x] **Replace Magic Numbers with Constants** (Issue #8)
  - Define METAMAGIC_STR_SIZE for buffer sizes (256)
  - Define INVALID_PREP_TIME for initial prep_time (99)
  - Define PREP_QUEUE_SENTINEL for sentinel values
  - **COMPLETED**: 2025-07-27 - Added the following constants:
    - METAMAGIC_STR_SIZE (256) - Buffer size for metamagic string building
    - INVALID_PREP_TIME (99) - Initial/invalid preparation time value  
    - PREP_QUEUE_SENTINEL ("-1 -1 -1 -1 -1") - Save file section terminator
    - KNOWN_SPELLS_SENTINEL ("-1 -1") - Known spells section terminator
    - Replaced all hardcoded values with these constants throughout the file

- [x] **Refactor Complex Functions** (Issue #9)
  - Split compute_spells_circle() (400+ lines)
    - **COMPLETED**: 2025-07-27 - Extracted 6 helper functions:
      - `validate_spell_for_class()` - Validates spell numbers for different class types
      - `calculate_metamagic_modifier()` - Calculates total metamagic circle adjustments with overflow protection
      - `apply_automatic_metamagic_reduction()` - Applies automatic metamagic feat reductions
      - `level_to_circle_conversion()` - Converts character level to spell circle based on caster type
      - `check_campaign_spell_override()` - Checks for campaign-specific spell circle overrides
    - Reduced repetitive code by consolidating class cases into simple calls to helper functions
    - Function is now much more maintainable and easier to understand
  - Split event_preparation() (200+ lines) - TODO
  - Simplify print_collection() nested logic - TODO

- [ ] **Standardize Error Messages** (Issue #10)
  - Ensure all errors use SYSERR: prefix
  - Consistent logging to syslog
  - Standardize error reporting approach

## TODO List - Security Improvements

- [x] **Add Input Validation for Commands**
  - Validate negative spell numbers
  - Check invalid class values
  - Verify metamagic flag combinations
  - **COMPLETED**: 2025-07-27 - Added comprehensive input validation:
    - Class validation: Check class is within 0 to NUM_CLASSES bounds
    - Spell validation: Check spell is within valid range (1 to MAX_SPELLS)
    - Spell type check: Verify spell is actually a spell (not a skill) using IS_SPELL()
    - Spell availability: Check spell is not disabled (min_position != POS_DEAD)
    - Metamagic validation: Verify only valid metamagic flags are used
    - Metamagic compatibility: Check spell can have requested metamagic applied
    - Circle validation: Verify computed circle is within valid bounds (1 to TOP_CIRCLE)
    - Added error logging with SYSERR prefix for all validation failures
    - Applied to both do_gen_preparation() and do_consign_to_oblivion() commands

- [x] **Add Integer Overflow Protection**
  - Preparation time calculations
  - Level calculations with bonuses
  - Array index calculations
  - **COMPLETED**: 2025-07-27 - Added comprehensive integer overflow protection:
    - compute_spells_prep_time(): Protected circle calculations, level bonus, stat bonus, concentration bonus, feat bonuses, room bonuses, and CONFIG multiplications
    - compute_spells_circle(): Protected metamagic_mod additions and spell_circle calculations
    - Added bounds checking for all arithmetic operations that could overflow
    - Added INT_MAX include for overflow checks
    - All potential overflow points now log SYSERR and use safe fallback values

- [x] **Add Queue Size Limits**
  - Prevent denial of service from excessive queue sizes
  - Define MAX_PREP_QUEUE_SIZE
  - Add checks when adding to queues
  - **COMPLETED**: 2025-07-27 - Added comprehensive queue size limits:
    - Defined constants in spell_prep.h: MAX_PREP_QUEUE_SIZE (125), MAX_COLLECTION_SIZE (250), MAX_INNATE_QUEUE_SIZE (125), MAX_KNOWN_SPELLS (250)
    - **UPDATED**: 2025-07-27 - Increased limits by 250% to better accommodate high-level play
    - Added size checks to prep_queue_add(), innate_magic_add(), collection_add(), and known_spells_add()
    - Each function now counts current queue size before adding and rejects with error message if limit exceeded
    - Added SYSERR logging when limits are exceeded with character name and class
    - Protected loading functions to prevent malicious save files from creating oversized queues
    - Load functions now check limits even when loading from file and log errors if file contains too many entries

